export const htmlTemplate = ({
															 title = 'iJewel3D Viewer', base, options = {
		showCard: true,
		showLogo: true
	}, config = null // Accept config parameter but don't use it in this template
														 }) => `
<!DOCTYPE html>
<html lang="en" >
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>${title}</title>
</head>
<body>
<div id="root"></div>
<script src='https://releases.ijewel3d.com/libs/mini-viewer/0.2.1/bundle.iife.js'></script>
<script >
const baseName = ${JSON.stringify(base)};

const basePath = "https://assets." + baseName + ".ijewel3d.com";

ijewelViewer.setupIjewelDriveViewer({
basePath: basePath,
ijewelViewerOptions: ${JSON.stringify(options)},
},
document.getElementById('root') // container element (change this to your container element)
);

window.addEventListener('webgi-viewer-ready', async (ev)=>{
console.log("iJewel3D Viewer is now ready.")
const viewer = ev.detail.viewer;
});

</script>
<style>
#root{
width: 100vw;
height: 100dvh;
}
</style>

</body>
</html>
`;
