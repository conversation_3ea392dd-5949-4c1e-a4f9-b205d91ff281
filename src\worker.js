// Version: 0.03

import { domainBaseMap } from './domainBaseMap';
import { domainBaseMapNew } from './domainBaseMapNew';
import { htmlTemplate } from './htmlTemplate';
import { htmlTemplateNew } from './htmlTemplateNew';


export default {
  async fetch(request, env, ctx) {
      const url = new URL(request.url)
      if(url.hostname === 'drive-link.ijewel.org')
          return new Response("Add a CNAME record to from your domain/subdomain to drive-link.ijewel.org to begin the iJewel3D Drive Setup");
      let domain = domainBaseMap[url.hostname];
      if(domain)
          return new Response(htmlTemplate({
              base: domain.base,
              title: domain.title,
          }), {
              headers: {
                  'content-type': 'text/html',
                  'cache-control': 'public, max-age=3600',
              }
          })
      domain = domainBaseMapNew[url.hostname];
      if(domain)
          return new Response(htmlTemplateNew({
              base: domain.base,
              title: domain.title,
          }), {
              headers: {
                  'content-type': 'text/html',
                  'cache-control': 'public, max-age=3600',
              }
          })

      return new Response("Setting up iJewel Drive. Come back later! Contact us for any <NAME_EMAIL>");

  },
};
