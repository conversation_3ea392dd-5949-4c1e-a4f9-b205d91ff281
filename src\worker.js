// Version: 0.03

import { domainBaseMap } from './domainBaseMap';
import { domainBaseMapNew } from './domainBaseMapNew';
import { htmlTemplate } from './htmlTemplate';
import { htmlTemplateNew } from './htmlTemplateNew';


export default {
  async fetch(request, env, ctx) {
      const url = new URL(request.url)

      // Debug: Log the hostname for testing
      console.log('Hostname received:', url.hostname);
      console.log('Full URL:', request.url);

      if(url.hostname === 'drive-link.ijewel.org')
          return new Response("Add a CNAME record to from your domain/subdomain to drive-link.ijewel.org to begin the iJewel3D Drive Setup");
      let domain = domainBaseMap[url.hostname];
      if(domain)
          return new Response(htmlTemplate({
              base: domain.base,
              title: domain.title,
          }), {
              headers: {
                  'content-type': 'text/html',
                  'cache-control': 'public, max-age=3600',
              }
          })
      domain = domainBaseMapNew[url.hostname];
      if(domain) {
          try {
              // Fetch drive config server-side for faster loading
              const baseApiUrl = `https://api.${domain.base}.ijewel3d.com/`;
              const configQueryParams = new URLSearchParams({ select: "id,val" });
              const driveConfigResponse = await fetch(baseApiUrl + "drive_config/select?" + configQueryParams.toString());

              let driveConfig = null;
              if (driveConfigResponse.ok) {
                  const configData = await driveConfigResponse.json();
                  // Convert array of {id, val} objects to a config object
                  driveConfig = {};
                  if (Array.isArray(configData)) {
                      configData.forEach(item => {
                          if (item.id && item.val !== undefined) {
                              driveConfig[item.id] = item.val;
                          }
                      });
                  }
              }

              return new Response(htmlTemplateNew({
                  base: domain.base,
                  title: domain.title,
                  config: driveConfig,
              }), {
                  headers: {
                      'content-type': 'text/html',
                      'cache-control': 'public, max-age=3600',
                  }
              });
          } catch (error) {
              console.error('Failed to fetch drive config:', error);
              // Fallback to template without config
              return new Response(htmlTemplateNew({
                  base: domain.base,
                  title: domain.title,
                  config: null,
              }), {
                  headers: {
                      'content-type': 'text/html',
                      'cache-control': 'public, max-age=3600',
                  }
              });
          }
      }

      return new Response("Setting up iJewel Drive. Come back later! Contact us for any <NAME_EMAIL>");

  },
};
