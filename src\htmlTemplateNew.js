export const htmlTemplateNew = ({
		title = 'iJewel3D Viewer',
		base,
		options = { showCard: true, showLogo: true },
		config = null,
	}) => {

	const pageTitle = config?.["app-name"] || title;

	//favicon
	const hasCustomLogo = config?.plan?.["allowed-features"]?.includes("custom-logo");
	const favicon = hasCustomLogo && config?.favicon ? config.favicon : "https://drive.ijewel3d.com/logo-sm.svg";

	// language, not really used now 
	const language = config?.language || "en-US";

	const currentWebgiVersion = "0.12.3"; // current version
	const webgiVersion = config?.["webgi-version"] || "latest";
	const viewerPath = config?.["viewer-path"] || `https://releases.ijewel3d.com/libs/mini-viewer/0.3.25/bundle.nowebgi.iife.js`;
	const useWebgi = viewerPath.includes('nowebgi');

	let finalWebgiVersion = webgiVersion;
	if (!finalWebgiVersion || finalWebgiVersion === "latest") {
		finalWebgiVersion = currentWebgiVersion;
	}

	return `
<!DOCTYPE html>
<html lang="${language}" >
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>${pageTitle}</title>
<link rel="icon" type="image/svg+xml" href="${favicon}" />
</head>
<body>
<div id="root"></div>
${useWebgi ? `<script src='https://dist.pixotronics.com/webgi/runtime/bundle-${finalWebgiVersion}.js'></script>` : ''}
<script src='${viewerPath}'></script>
<script >
const baseName = ${JSON.stringify(base)};
const modelId = (new URLSearchParams(window.location.search)).get('id');
if(!modelId) alert('No file here');

const basePath = "https://assets." + baseName + ".ijewel3d.com";

ijewelViewer.loadModelById(
	modelId,
	baseName,
	document.getElementById('root'),
	${JSON.stringify(options)}
);

window.addEventListener('webgi-viewer-ready', async (ev)=>{
	console.log("iJewel3D Viewer is now ready.")
	const viewer = ev.detail.viewer;
});

</script>
<style>
#root{
width: 100vw;
height: 100dvh;
}
</style>

</body>
</html>
`;
};
